# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Expo
.expo/
.expo-shared/
dist/
web-build/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env*

# typescript
*.tsbuildinfo

# IDE
.vscode/
.idea/
*.swp
*.swo

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Android
android/app/build/
android/app/debug
android/app/release

# iOS
ios/build/
ios/Pods/
ios/Podfile.lock
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Temporary files
*.tmp
*.temp

# Logs
logs
*.log

# Other
coverage/
*.lcov
.nyc_output 
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli
.yarn/

keys/

google-services.json
GoogleService-Info.plist

build-*
