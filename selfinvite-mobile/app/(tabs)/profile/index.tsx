import React from 'react';
import { ScrollView, StyleSheet, View, ActivityIndicator } from 'react-native';
import { Surface, Text, Avatar, Button, List, Divider } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import SocialAuth from '../../../components/SocialAuth';
import { AppleSocialAuth } from '../../../components/AppleSocialAuth';
import Auth from '../../../components/Auth';
import { useAuthStore } from '../../../src/stores/authStore';
import SignOut from '../../../components/SignOut';
import { useRouter } from 'expo-router';
import { notificationsApi } from '../../../src/api/notificationsApi';
import { useUserProfile, useUserStats, useUserBadges, useUserPreferences } from '../../../src/hooks';
import logger from '../../../src/common/logger';
import type { UserProfile, UserPreferences } from '../../../src/types';
import { SpinnerDemo, NotificationTestButton, TranslationTest, ExpoEnvironmentSwitcher, CameraDemo } from '../../../src/components';

export default function ProfileScreen() {
  const theme = useTheme();
  const { isAuthenticated, user } = useAuthStore();
  const router = useRouter();
  const [showDemo, setShowDemo] = React.useState(false);
  const [showNotifications, setShowNotifications] = React.useState(false);
  const [showTranslations, setShowTranslations] = React.useState(false);
  const [showEnvConfig, setShowEnvConfig] = React.useState(false);
  const [showCamera, setShowCamera] = React.useState(false);
  
  // Fetch user profile data
  const { data: userProfile, isLoading: profileLoading, error: profileError } = useUserProfile();
  const { data: userStats, isLoading: statsLoading } = useUserStats();
  const { data: userBadges } = useUserBadges();
  const { data: userPreferences } = useUserPreferences();

  // Type assertions for better type safety
  const profile = userProfile as UserProfile | undefined;
  const stats = userStats as { eventsHosted: number; eventsJoined: number; averageRating: number } | undefined;
  const badges = userBadges as string[] | undefined;
  const preferences = userPreferences as UserPreferences | undefined;

  // Helper functions
  const getDisplayName = () => {
    if (profile?.firstName && profile?.lastName) {
      return `${profile.firstName} ${profile.lastName}`;
    }
    if (profile?.username) {
      return profile.username;
    }
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name;
    }
    if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'User';
  };

  const getInitials = () => {
    const name = getDisplayName();
    const parts = name.split(' ');
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  const getLocation = () => {
    if (profile?.city && profile?.country) {
      return `${profile.city}, ${profile.country}`;
    }
    return 'Location not set';
  };

  const getMemberSince = () => {
    if (profile?.createdAt) {
      const date = new Date(profile.createdAt);
      return `Member since ${date.getFullYear()}`;
    }
    if (user?.created_at) {
      const date = new Date(user.created_at);
      return `Member since ${date.getFullYear()}`;
    }
    return 'Member since 2023';
  };

  // Show loading state
  if (isAuthenticated && (profileLoading || statsLoading)) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading profile...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (isAuthenticated && profileError) {
    logger.error('Profile loading error:', profileError);
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
      <ScrollView style={styles.content}>
      {!isAuthenticated ? (
          <>
            <SocialAuth />
            <AppleSocialAuth />
            <Auth />
          </>
        ) : (
        <>
        <Surface style={[styles.profileHeader, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.avatarSection}>
            {profile?.avatar_url ? (
              <Avatar.Image 
                size={80} 
                source={{ uri: profile.avatar_url }} 
                style={{ backgroundColor: theme.colors.primary }} 
              />
            ) : (
              <Avatar.Text 
                size={80} 
                label={getInitials()} 
                style={{ backgroundColor: theme.colors.primary }} 
              />
            )}
            <View style={styles.userInfo}>
              <Text style={[styles.userName, { color: theme.colors.onSurface }]}>
                {getDisplayName()}
              </Text>
              <Text style={[styles.userLocation, { color: theme.colors.onSurface }]}>
                📍 {getLocation()}
              </Text>
              <Text style={[styles.userMemberSince, { color: theme.colors.onSurface }]}>
                {getMemberSince()}
              </Text>
              {profile?.bio && (
                <Text style={[styles.userBio, { color: theme.colors.onSurface }]}>
                  {profile.bio}
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
                {stats?.eventsHosted || 0}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onSurface }]}>Events Hosted</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
                {stats?.eventsJoined || 0}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onSurface }]}>Events Attended</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
                {stats?.averageRating ? stats.averageRating.toFixed(1) : '0.0'}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onSurface }]}>Rating</Text>
            </View>
          </View>
          
          {badges && badges.length > 0 && (
            <View style={styles.badgesSection}>
              <Text style={[styles.badgesTitle, { color: theme.colors.onSurface }]}>Badges</Text>
              <View style={styles.badgesContainer}>
                {badges.map((badge: string, index: number) => (
                  <View key={index} style={[styles.badge, { backgroundColor: theme.colors.primaryContainer }]}>
                    <Text style={[styles.badgeText, { color: theme.colors.onPrimaryContainer }]}>
                      {badge}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </Surface>


        
        <Surface style={[styles.quickActions, { backgroundColor: theme.colors.surface }]}>
          <Button 
            mode="contained" 
            icon="plus" 
            style={[styles.createEventButton, { backgroundColor: theme.colors.primary }]}
            labelStyle={{ color: '#FFFFFF' }}
            onPress={() => router.push('/(tabs)/profile/create-event')}
          >
            Create New Event
          </Button>
        </Surface>

        
        <Surface style={[styles.menuSection, { backgroundColor: theme.colors.surface }]}>
          <List.Item
            title="Edit Profile"
            description="Update your personal information"
            left={(props) => <List.Icon {...props} icon="account-edit" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => router.push('/(tabs)/profile/edit-profile')}
          />
          <Divider />
          
          <List.Item
            title="My Reviews & Feedbacks"
            description="See reviews and feedbacks"
            left={(props) => <List.Icon {...props} icon="star" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          <Divider />
          
          <List.Item
            title="Payment Methods"
            description="Manage your payment options"
            left={(props) => <List.Icon {...props} icon="credit-card" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          <Divider />
          
          <List.Item
            title="Dietary Preferences"
            description={
              preferences?.preferences && preferences.preferences.length > 0
                ? preferences.preferences.join(', ')
                : preferences?.intolerances && preferences.intolerances.length > 0
                ? `Intolerances: ${preferences.intolerances.join(', ')}`
                : "Set your food preferences"
            }
            left={(props) => <List.Icon {...props} icon="food-apple" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          <Divider />
          
          <List.Item
            title="Notifications"
            description="Manage your notification settings"
            left={(props) => <List.Icon {...props} icon="bell" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => router.push('/(tabs)/profile/notifications')}
          />
          <Divider />
          
          <List.Item
            title="Privacy & Safety"
            description="Blocked users and privacy settings"
            left={(props) => <List.Icon {...props} icon="shield-account" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {
              notificationsApi.sendTestNotification('user-id', 'Test Notification', 'This is a test notification');
            }}
          />
        </Surface>

        
        <Surface style={[styles.supportSection, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Support</Text>
          
          <List.Item
            title="Help & FAQ"
            left={(props) => <List.Icon {...props} icon="help-circle" color={theme.colors.onSurface} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          <Divider />
          
          <List.Item
            title="Contact Support"
            left={(props) => <List.Icon {...props} icon="email" color={theme.colors.onSurface} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          <Divider />
          
          <List.Item
            title="About"
            left={(props) => <List.Icon {...props} icon="information" color={theme.colors.onSurface} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
        </Surface>

        <SignOut />
        </>
        )}
        {/* Demo & Testing Section */}
        <Surface style={[styles.demoSection, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Demo & Testing</Text>
          
          <List.Item
            title="Spinner Demo"
            description="Test different loading animations"
            left={(props) => <List.Icon {...props} icon="loading" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon={showDemo ? "chevron-up" : "chevron-down"} />}
            onPress={() => setShowDemo(!showDemo)}
          />
          
          {showDemo && (
            <View style={styles.demoContainer}>
              <SpinnerDemo />
            </View>
          )}

          <Divider />

          <List.Item
            title="Notification Testing"
            description="Test push notifications and messaging"
            left={(props) => <List.Icon {...props} icon="bell-ring" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon={showNotifications ? "chevron-up" : "chevron-down"} />}
            onPress={() => setShowNotifications(!showNotifications)}
          />
          
          {showNotifications && (
            <View style={styles.demoContainer}>
              <NotificationTestButton />
            </View>
          )}

          <Divider />

          <List.Item
            title="Translation Testing"
            description="Test i18n translations and localization"
            left={(props) => <List.Icon {...props} icon="translate" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon={showTranslations ? "chevron-up" : "chevron-down"} />}
            onPress={() => setShowTranslations(!showTranslations)}
          />
          
          {showTranslations && (
            <View style={styles.demoContainer}>
              <TranslationTest />
            </View>
          )}

          <Divider />

          <List.Item
            title="Environment Configuration"
            description="Switch between development, preview, and production configs"
            left={(props) => <List.Icon {...props} icon="cog" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon={showEnvConfig ? "chevron-up" : "chevron-down"} />}
            onPress={() => setShowEnvConfig(!showEnvConfig)}
          />
          
          {showEnvConfig && (
            <View style={styles.demoContainer}>
              <ExpoEnvironmentSwitcher />
            </View>
          )}

          <Divider />

          <List.Item
            title="Camera Testing"
            description="Test camera functionality for photos and videos"
            left={(props) => <List.Icon {...props} icon="camera" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon={showCamera ? "chevron-up" : "chevron-down"} />}
            onPress={() => setShowCamera(!showCamera)}
          />
          
          {showCamera && (
            <View style={styles.demoContainer}>
              <CameraDemo />
            </View>
          )}
        </Surface>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  profileHeader: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
    marginBottom: 4,
  },
  avatarSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  userInfo: {
    marginLeft: 16,
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userLocation: {
    fontSize: 14,
    marginBottom: 2,
  },
  userMemberSince: {
    fontSize: 12,
    opacity: 0.7,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  quickActions: {
    padding: 16,
    marginBottom: 8,
  },
  createEventButton: {
    borderRadius: 8,
  },
  menuSection: {
    marginBottom: 8,
  },
  supportSection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    padding: 16,
    paddingBottom: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  userBio: {
    fontSize: 14,
    marginTop: 4,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  badgesSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  badgesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  badge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  demoSection: {
    marginBottom: 8,
  },
  demoContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
}); 