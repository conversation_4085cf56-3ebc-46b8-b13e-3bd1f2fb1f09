import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { SocialAuth, GoogleAuthConfig, FacebookAuthConfig } from '../lib/auth';

interface SocialLoginButtonProps {
  provider: 'google' | 'facebook';
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

export default function SocialLoginButton({ 
  provider, 
  onSuccess, 
  onError 
}: SocialLoginButtonProps) {
  
  const handleSocialLogin = async () => {
    try {
      const config = provider === 'google' ? GoogleAuthConfig : FacebookAuthConfig;
      const socialAuth = new SocialAuth(config);
      
      const result = await socialAuth.startAuthSession();
      
      if (result.type === 'success') {
        console.log('Auth session successful, redirecting to callback...');
        onSuccess?.();
      } else if (result.type === 'cancel') {
        console.log('User cancelled authentication');
      } else {
        console.error('Auth session failed:', result);
        onError?.(result);
      }
    } catch (error) {
      console.error('Social login error:', error);
      Alert.alert('Error', 'Failed to start authentication. Please try again.');
      onError?.(error);
    }
  };

  const getButtonText = () => {
    return provider === 'google' ? 'Continue with Google' : 'Continue with Facebook';
  };

  const getButtonStyle = () => {
    return provider === 'google' ? styles.googleButton : styles.facebookButton;
  };

  return (
    <TouchableOpacity 
      style={[styles.button, getButtonStyle()]} 
      onPress={handleSocialLogin}
    >
      <Text style={styles.buttonText}>{getButtonText()}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginVertical: 8,
    alignItems: 'center',
  },
  googleButton: {
    backgroundColor: '#4285F4',
  },
  facebookButton: {
    backgroundColor: '#1877F2',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
