import React, { useState } from 'react';
import { StyleSheet, View, Platform } from 'react-native';
import { supabaseAuth } from '../lib/supabase';
import { Button, TextInput } from 'react-native-paper';
import * as Device from 'expo-device';
import log from '@/common/logger';
import { showErrorDialog, showSuccessDialog, showConfirmationDialog, useAuthStore, useDialogStore } from '@/stores';
import { ApiResponse } from '@/types/api';
import { paymentsApi } from '@/api/paymentsApi';
import { useNotifications } from '../src/hooks/useNotifications';
import { notificationsApi } from '../src/api/notificationsApi';

export default function Auth() {
  const { setSession, setUser } = useAuthStore();
  const { requestPermissions, isEnabled, pushToken } = useNotifications();
  const { showDialog } = useDialogStore();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [loading, setLoading] = useState(false);

  async function signInWithEmail() {
    setLoading(true);
    const { error, data } = await supabaseAuth.auth.signInWithPassword({
      email: email,
      password: password,
    });
    log.info('signInWithEmail', { error, data });
    if (error) showErrorDialog(error.message);
    if (data && data.session && data.user) {
      setSession(data.session);
      setUser(data.user);
      showSuccessDialog('Successfully signed in!');

      // Verify if Stripe user is present and create one if needed
      await verifyAndCreateStripeUser(data.user.id);

      // Update push token on backend and handle notification permissions
      await handleNotificationPermissions();

    }
    setLoading(false);
  }

  async function signUpWithEmail() {
    setLoading(true);
    const {
      data: { session, user },
      error,
    } = await supabaseAuth.auth.signUp({
      email: email,
      password: password,
    });

    if (error) showErrorDialog(error.message);
    if (!session || !user)
      showErrorDialog('Please check your inbox for email verification!');
    if (session && user) {
      setSession(session);
      setUser(user);
      showSuccessDialog('Successfully signed up!');
      
      // Verify if Stripe user is present and create one if needed
      await verifyAndCreateStripeUser(user.id);

      // Update push token on backend and handle notification permissions
      await handleNotificationPermissions();
    }
    setLoading(false);
  }

  async function verifyAndCreateStripeUser(userId: string) {
    try {
      log.info('Verifying Stripe user for userId:', userId);
      
      const stripeUserResponse = await paymentsApi.fetchStripeUserDetails();

      if (stripeUserResponse.status === 200 && stripeUserResponse.data) {
        log.info('Stripe user already exists:', stripeUserResponse.data);
        return;
      }
      
      // If no Stripe user exists, create one
      log.info('Creating new Stripe customer for userId:', userId);
      const createCustomerResponse = await paymentsApi.createStripeCustomer(userId);
      
      if (createCustomerResponse.success === true) {
        log.info('Successfully created Stripe customer:', createCustomerResponse.customer_id);
      } else {
        log.error('Failed to create Stripe customer:', createCustomerResponse.message);
      }
    } catch (error) {
      log.error('Error in Stripe user verification/creation:', error);
    }
  }

  async function updatePushTokenOnBackend() {
    try {
      if (!pushToken) {
        log.warn('No push token available to update on backend');
        return;
      }

      // Get device information
      const deviceId = Device.osInternalBuildId || Device.modelId || 'unknown-device';
      const platform = Platform.OS as 'ios' | 'android';

      log.info('Updating push token on backend:', { pushToken, deviceId, platform });
      
      await notificationsApi.registerToken({
        token: pushToken,
        type: 'expo',
        deviceId,
        platform,
      });

      log.info('Successfully updated push token on backend');
    } catch (error) {
      log.error('Failed to update push token on backend:', error);
      // Don't show error to user as this is not critical for auth flow
    }
  }

  async function handleNotificationPermissions() {
    try {
      if (!isEnabled) {
        log.info('Notifications not enabled, requesting permissions...');
        
        const granted = await requestPermissions();
        
        if (granted) {
          log.info('Notification permissions granted');
          // Update push token after permissions are granted
          await updatePushTokenOnBackend();
        } else {
          log.info('Notification permissions denied by user');
          // Show a gentle prompt about the benefits of notifications
          showDialog({
            type: 'info',
            title: 'Notifications',
            message: 'You can enable notifications later in your profile settings to receive updates about your events and messages.',
            buttons: [
              {
                text: 'OK',
                mode: 'contained',
                style: 'primary',
              },
            ],
            dismissible: true,
          });
        }
      } else {
        log.info('Notifications already enabled, updating push token...');
        // Update push token if notifications are already enabled
        await updatePushTokenOnBackend();
      }
    } catch (error) {
      log.error('Error handling notification permissions:', error);
      // Don't show error to user as this is not critical for auth flow
    }
  }

  return (
    <View style={styles.container}>
      <View style={[styles.verticallySpaced, styles.mt20]}>
        <TextInput
          label="Email"
          // leftIcon removed: not supported by react-native-paper
          onChangeText={(text: string) => setEmail(text)}
          value={email}
          placeholder="<EMAIL>"
          autoCapitalize={'none'}
        />
      </View>
      <View style={styles.verticallySpaced}>
        <TextInput
          label="Password"
          // leftIcon removed: not supported by react-native-paper
          onChangeText={(text: string) => setPassword(text)}
          value={password}
          secureTextEntry={true}
          placeholder="Password"
          autoCapitalize={'none'}
        />
      </View>
      <View style={[styles.verticallySpaced, styles.mt20]}>
        <Button disabled={loading} onPress={() => signInWithEmail()}>
          Sign in
        </Button>
      </View>
      <View style={styles.verticallySpaced}>
        <Button disabled={loading} onPress={() => signUpWithEmail()}>
          Sign up
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    padding: 12,
  },
  verticallySpaced: {
    paddingTop: 4,
    paddingBottom: 4,
    alignSelf: 'stretch',
  },
  mt20: {
    marginTop: 20,
  },
});
