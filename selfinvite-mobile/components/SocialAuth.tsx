import React, { useState } from 'react'
import { StyleSheet, View } from 'react-native'
import * as AuthSession from 'expo-auth-session';
import supabase from '../lib/supabase'
import { Button, TextInput } from 'react-native-paper'
import * as WebBrowser from 'expo-web-browser';
import log from '@/common/logger'

export default function SocialAuth() {
  const [loading, setLoading] = useState(false)

  const signInWithGoogle = async () => {
    setLoading(true)
    const redirectUri = AuthSession.makeRedirectUri({
      scheme: 'selfinvite', // Let Expo determine the scheme
      path: 'auth/callback',
      isTripleSlashed: false,
    })
    
    log.info('redirectUri login with google', redirectUri)
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUri,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      }
    })
    log.info('data login with google', data)
    log.info('error', error)
  
    if (data?.url) {
      const result = await WebBrowser.openAuthSessionAsync(
        data.url,
        redirectUri,
      )
  
      log.info('result login with google', result)
      if (result.type === 'success') {
        // User is now authenticated - check session
        const { data: session } = await supabase.auth.getSession()
        setLoading(false)
        return { data: session }
      }
    }
    setLoading(false)
    return { error: 'Authentication failed' }
  }

  return (
    <View style={styles.container}>
      <View style={styles.verticallySpaced}>
        <Button
          mode="contained"
          icon="google"
          disabled={loading}
          onPress={() => signInWithGoogle()}
          style={{ marginTop: 16 }}
        >
          Sign in with Google
        </Button>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    padding: 12,
  },
  verticallySpaced: {
    paddingTop: 4,
    paddingBottom: 4,
    alignSelf: 'stretch',
  },
  mt20: {
    marginTop: 20,
  },
})