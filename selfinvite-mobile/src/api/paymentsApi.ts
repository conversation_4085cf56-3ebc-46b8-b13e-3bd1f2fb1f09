import { apiClient } from '../utils/apiClient'
import type { ApiResponse } from '../types'

// Payment-related types
export interface PaymentIntent {
  paymentId: string
  clientSecret: string
  amount: number
  currency: string
  status: string // 'pending' | 'succeeded' | 'canceled' | 'failed'
  eventId: string
  bookingId: string
  createdAt: string
  updatedAt: string
}

export interface PaymentIntentResponse {
  payment_id: string
  stripe_payment_id: string
  client_secret: string
  amount: number
  currency: string
  status: string
  event_id: string
  message: string
  platform_fee: number
  booking_id: string
  created_at: string
  updated_at: string
}

export interface Payment {
  id: string
  amount: number
  currency: string
  status: 'pending' | 'succeeded' | 'failed'
  paymentIntentId: string
  eventId: string
  userId: string
  createdAt: string
}

export interface RefundRequest {
  paymentId: string
  amount: number
  reason: string
}

export interface Refund {
  id: string
  paymentId: string
  amount: number
  reason: string
  status: 'pending' | 'succeeded' | 'failed'
  createdAt: string
}

export interface TransactionHistory {
  transactions: Payment[]
  total: number
  limit: number
  offset: number
}

export interface StripeCustomerResponse {
  customer_id: string
  user_id: string
  success: boolean
  message: string
}

export const paymentsApi = {
  // Get transaction history
  async getTransactionHistory(params?: { limit?: number; offset?: number }): Promise<TransactionHistory> {
    const queryParams = new URLSearchParams()
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.offset) queryParams.append('offset', params.offset.toString())
    
    const query = queryParams.toString()
    return apiClient.get<TransactionHistory>(`/payments/transactions${query ? `?${query}` : ''}`)
  },

  // Get single transaction
  async getTransaction(transactionId: string): Promise<Payment> {
    return apiClient.get<Payment>(`/payments/transactions/${transactionId}`)
  },

  // Get payment intents
  async getPaymentIntents(): Promise<PaymentIntent[]> {
    return apiClient.get<PaymentIntent[]>('/payments/intents')
  },

  // Get single payment intent
  async getPaymentIntent(intentId: string): Promise<PaymentIntent> {
    return apiClient.get<PaymentIntent>(`/payments/intents/${intentId}`)
  },

  // Get refunds
  async getRefunds(): Promise<Refund[]> {
    return apiClient.get<Refund[]>('/payments/refunds')
  },

  // Get single refund
  async getRefund(refundId: string): Promise<Refund> {
    return apiClient.get<Refund>(`/payments/refunds/${refundId}`)
  },

  // Create payment intent
  async createPaymentIntent(paymentData: { currency: string; bookingId: string }): Promise<PaymentIntent> {
    const response = await apiClient.post<PaymentIntentResponse>('/payments/create', {
      currency: paymentData.currency,
      booking_id: paymentData.bookingId,
    })
    return {
      paymentId: response.payment_id,
      clientSecret: response.client_secret,
      amount: response.amount,
      currency: response.currency,
      status: response.status as 'pending' | 'succeeded' | 'canceled' | 'failed',
      eventId: response.event_id,
      bookingId: response.booking_id,
      createdAt: response.created_at,
      updatedAt: response.updated_at,
    }
  },

  // Confirm payment
  async confirmPayment(intentId: string, paymentMethodId: string): Promise<Payment> {
    return apiClient.post<Payment>(`/payments/intents/${intentId}/confirm`, { paymentMethodId })
  },

  // Request refund
  async requestRefund(refundData: RefundRequest): Promise<Refund> {
    return apiClient.post<Refund>('/payments/refunds', refundData)
  },

  // Cancel payment intent
  async cancelPaymentIntent(intentId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/payments/intents/${intentId}`)
  },

  // Update payment method
  async updatePaymentMethod(paymentMethodId: string, data: any): Promise<ApiResponse> {
    return apiClient.put<ApiResponse>(`/payments/methods/${paymentMethodId}`, data)
  },

  // Delete payment method
  async deletePaymentMethod(paymentMethodId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/payments/methods/${paymentMethodId}`)
  },

  // add call to fetch stripe user details, and create stripe customer if not exists
  async fetchStripeUserDetails(): Promise<ApiResponse> {
    return apiClient.get<ApiResponse>('/payments/customers/')
  },

  // create stripe customer if not exists
  async createStripeCustomer(userId: string): Promise<StripeCustomerResponse> {
    return apiClient.post<StripeCustomerResponse>(`/payments/customers`, { userId })
  },
} 