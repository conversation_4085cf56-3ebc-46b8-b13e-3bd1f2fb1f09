import { apiClient } from '../utils/apiClient'
import type { Message, Conversation, SendMessageRequest, PaginatedResponse, ApiResponse } from '../types'

export const messagesApi = {
  // Get user's conversations
  async getConversations(): Promise<Conversation[]> {
    const response = await apiClient.get<any>('/messages/get-threads')
    return response.threads || []
  },

  // Get single conversation
  async getConversation(conversationId: string): Promise<Conversation> {
    return apiClient.get<Conversation>(`/conversations/get-thread-messages?event_booking_id=${conversationId}`)
  },

  // Get messages for a conversation
  async getMessages(conversationId: string, params?: { limit?: number; before?: string }): Promise<PaginatedResponse<Message>> {
    const queryParams = new URLSearchParams()
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.before) queryParams.append('before', params.before)
    
    const query = queryParams.toString()
    return apiClient.get<PaginatedResponse<Message>>(`/conversations/${conversationId}/messages${query ? `?${query}` : ''}`)
  },

  // Get unread message count
  async getUnreadCount(): Promise<{ count: number }> {
    return apiClient.get<{ count: number }>('/messages/unread-count')
  },

  // Send message
  async sendMessage(conversationId: string, message: SendMessageRequest): Promise<Message> {
    return apiClient.post<Message>(`/conversations/${conversationId}/messages`, message)
  },

  // Mark messages as read
  async markAsRead(conversationId: string, messageIds: string[]): Promise<ApiResponse> {
    return apiClient.patch<ApiResponse>(`/conversations/${conversationId}/messages/read`, { messageIds })
  },

  // Delete message
  async deleteMessage(conversationId: string, messageId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/conversations/${conversationId}/messages/${messageId}`)
  },

  // Create new conversation
  async createConversation(participantIds: string[]): Promise<Conversation> {
    return apiClient.post<Conversation>('/conversations', { participantIds })
  },

  // Leave conversation
  async leaveConversation(conversationId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/conversations/${conversationId}/leave`)
  },

  // Block user
  async blockUser(userId: string): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>(`/users/${userId}/block`)
  },

  // Unblock user
  async unblockUser(userId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/users/${userId}/block`)
  },
} 