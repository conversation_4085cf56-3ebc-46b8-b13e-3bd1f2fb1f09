import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface, Text, Chip, Divider, useTheme } from 'react-native-paper';
import Constants from 'expo-constants';

interface ExpoEnvironmentSwitcherProps {
  style?: any;
}

export function ExpoEnvironmentSwitcher({ style }: ExpoEnvironmentSwitcherProps) {
  const theme = useTheme();
  
  // Get current environment from Constants
  const currentEnv = Constants.expoConfig?.extra?.appEnv || 'development';
  const environments = Constants.expoConfig?.extra?.environments || {};
  const currentConfig = environments[currentEnv] || {};
  
  // Environment display info
  const getEnvInfo = (env: string) => {
    switch (env) {
      case 'development':
        return { label: 'Development', color: '#4CAF50', description: 'Local development environment' };
      case 'preview':
        return { label: 'Preview', color: '#FF9800', description: 'Preview/testing environment' };
      case 'production':
        return { label: 'Production', color: '#F44336', description: 'Live production environment' };
      default:
        return { label: env, color: '#9E9E9E', description: 'Unknown environment' };
    }
  };
  
  const envInfo = getEnvInfo(currentEnv);

  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surfaceVariant }, style]}>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Expo Configuration
      </Text>
      
      <View style={styles.currentEnvContainer}>
        <Text style={[styles.currentEnvLabel, { color: theme.colors.onSurface }]}>
          Current Environment:
        </Text>
        <Chip 
          mode="outlined" 
          style={[styles.currentEnvChip, { borderColor: envInfo.color }]}
          textStyle={{ color: envInfo.color }}
        >
          {envInfo.label}
        </Chip>
      </View>

      <Text style={[styles.description, { color: theme.colors.onSurface }]}>
        {envInfo.description}
      </Text>

      <Divider style={styles.divider} />

      <View style={styles.configInfo}>
        <Text style={[styles.configTitle, { color: theme.colors.onSurface }]}>
          Current Configuration:
        </Text>
        
        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Supabase URL:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]} numberOfLines={1}>
            {currentConfig.supabaseUrl ? 
              `${currentConfig.supabaseUrl.substring(0, 30)}...` : 
              'Not set'
            }
          </Text>
        </View>

        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Algolia App ID:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]}>
            {currentConfig.algoliaAppId || 'Not set'}
          </Text>
        </View>

        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Stripe Key:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]}>
            {currentConfig.stripePublishableKey ? 
              `${currentConfig.stripePublishableKey.substring(0, 20)}...` : 
              'Not set'
            }
          </Text>
        </View>

        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Mapbox Token:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]}>
            {currentConfig.mapboxAccessToken ? 
              `${currentConfig.mapboxAccessToken.substring(0, 20)}...` : 
              'Not set'
            }
          </Text>
        </View>

        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Google Maps Key:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]}>
            {currentConfig.googleMapsApiKey ? 
              `${currentConfig.googleMapsApiKey.substring(0, 20)}...` : 
              'Not set'
            }
          </Text>
        </View>
      </View>

      <View style={styles.infoSection}>
        <Text style={[styles.infoTitle, { color: theme.colors.onSurface }]}>
          How it works:
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Configuration is defined in app.config.js
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Environment-specific configs are loaded at build time
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Current environment: {currentEnv}
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Changes require a new build to take effect
        </Text>
      </View>
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  currentEnvContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentEnvLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  currentEnvChip: {
    marginLeft: 8,
  },
  description: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  configInfo: {
    marginBottom: 16,
  },
  configTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  configRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  configLabel: {
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
  configValue: {
    fontSize: 11,
    opacity: 0.8,
    flex: 1,
    textAlign: 'right',
  },
  infoSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 11,
    opacity: 0.7,
    marginBottom: 4,
  },
});

export default ExpoEnvironmentSwitcher;
