import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Surface, Text, Button, useTheme } from 'react-native-paper';
import { CameraMediaPicker, ProfileCamera } from './index';
import type { MediaItem } from './MediaPicker';

interface CameraDemoProps {
  style?: any;
}

export const CameraDemo: React.FC<CameraDemoProps> = ({ style }) => {
  const theme = useTheme();
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);
  const [profilePhoto, setProfilePhoto] = useState<string>('');

  const handleMediaSelected = (media: MediaItem[]) => {
    setSelectedMedia(media);
    console.log('Selected media:', media);
  };

  const handleProfilePhotoTaken = (uri: string) => {
    setProfilePhoto(uri);
    Alert.alert('Photo Taken', 'Profile photo captured successfully!');
  };

  const handleProfilePhotoSelected = (uri: string) => {
    setProfilePhoto(uri);
    Alert.alert('Photo Selected', 'Profile photo selected successfully!');
  };

  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surfaceVariant }, style]}>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Camera Demo
      </Text>
      
      <Text style={[styles.description, { color: theme.colors.onSurface }]}>
        Test the camera functionality for events and profile photos
      </Text>

      {/* Profile Camera Demo */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Profile Photo
        </Text>
        <ProfileCamera
          currentAvatar={profilePhoto}
          onPhotoTaken={handleProfilePhotoTaken}
          onPhotoSelected={handleProfilePhotoSelected}
        />
      </View>

      {/* Event Media Demo */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Event Media
        </Text>
        <CameraMediaPicker
          onMediaSelected={handleMediaSelected}
          maxFiles={3}
          allowedTypes={['image', 'video']}
          placeholder="Add photos for your event"
        />
        
        {selectedMedia.length > 0 && (
          <View style={styles.mediaInfo}>
            <Text style={[styles.mediaCount, { color: theme.colors.onSurface }]}>
              {selectedMedia.length} media item(s) selected
            </Text>
            {selectedMedia.map((item, index) => (
              <Text key={index} style={[styles.mediaItem, { color: theme.colors.onSurface }]}>
                • {item.name} ({item.type})
              </Text>
            ))}
          </View>
        )}
      </View>

      <View style={styles.infoSection}>
        <Text style={[styles.infoTitle, { color: theme.colors.onSurface }]}>
          Camera Features:
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Take photos directly with camera
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Select from photo library
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Front/back camera switching
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Image optimization and compression
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Square cropping for profile photos
        </Text>
      </View>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  mediaInfo: {
    marginTop: 12,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  mediaCount: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  mediaItem: {
    fontSize: 12,
    marginBottom: 4,
  },
  infoSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 4,
  },
});

export default CameraDemo;
