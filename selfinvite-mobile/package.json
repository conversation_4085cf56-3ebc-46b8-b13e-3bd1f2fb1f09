{"name": "selfinvite-mobile", "version": "1.0.0", "description": "Selfinvite Mobile App - Social Dining Platform", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:android:production": "eas build --platform android --profile production", "build:ios:production": "eas build --platform ios --profile production", "submit:android": "eas submit --platform android --profile production", "submit:ios": "eas submit --platform ios --profile production", "deploy:android": "yarn build:android:production && yarn submit:android", "deploy:ios": "yarn build:ios:production && yarn submit:ios", "deploy:all": "yarn deploy:android && yarn deploy:ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/metro-runtime": "~6.1.2", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-community/datetimepicker": "8.4.4", "@react-native-community/slider": "5.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@stripe/stripe-react-native": "0.50.3", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.83.0", "@types/moment": "^2.11.29", "algoliasearch": "^5.37.0", "dotenv": "^17.2.2", "expo": "^54.0.0", "expo-apple-authentication": "~8.0.7", "expo-auth-session": "~7.0.8", "expo-camera": "~17.0.8", "expo-constants": "~18.0.8", "expo-crypto": "~15.0.7", "expo-device": "~8.0.7", "expo-file-system": "~19.0.14", "expo-font": "~14.0.8", "expo-image": "~3.0.8", "expo-image-manipulator": "~14.0.7", "expo-image-picker": "~17.0.8", "expo-linking": "~8.0.8", "expo-localization": "~17.0.7", "expo-location": "~19.0.7", "expo-media-library": "~18.1.1", "expo-notifications": "~0.32.11", "expo-router": "~6.0.5", "expo-secure-store": "~15.0.7", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-system-ui": "~6.0.7", "expo-web-browser": "~15.0.7", "i18n-js": "^4.5.1", "moment": "^2.30.1", "react": "19.1.0", "react-hook-form": "^7.60.0", "react-instantsearch-core": "^7.16.3", "react-native": "0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-image-picker": "^8.2.1", "react-native-logs": "^5.3.0", "react-native-maps": "1.20.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-simple-radio-button": "^2.7.4", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-worklets": "0.5.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.28.0", "@types/react": "~19.1.10", "eslint": "^9.31.0", "eslint-config-expo": "~10.0.0", "jest": "~29.7.0", "prettier": "^3.6.2", "typescript": "~5.9.2"}, "packageManager": "yarn@1.22.22", "private": true}