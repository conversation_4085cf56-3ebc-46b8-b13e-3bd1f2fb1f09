{"cli": {"version": ">= 16.3.1", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "env": {"YARN_ENABLE_IMMUTABLE_INSTALLS": "false", "EXPO_PUBLIC_APP_ENV": "development", "EXPO_PUBLIC_SUPABASE_MEDIAS_EVENT_OFFER_PATH": "event_offer_medias", "EXPO_PUBLIC_ALGOLIA_APP_ID": "DP7DN903MK", "__DEV__": "0"}, "ios": {"resourceClass": "medium"}, "android": {"buildType": "apk"}}, "preview": {"distribution": "internal", "env": {"YARN_ENABLE_IMMUTABLE_INSTALLS": "false", "EXPO_PUBLIC_APP_ENV": "preview", "EXPO_PUBLIC_SUPABASE_MEDIAS_EVENT_OFFER_PATH": "event_offer_medias", "EXPO_PUBLIC_ALGOLIA_APP_ID": "DP7DN903MK", "__DEV__": "0"}, "ios": {"resourceClass": "medium"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"autoIncrement": true, "env": {"YARN_ENABLE_IMMUTABLE_INSTALLS": "false", "EXPO_PUBLIC_APP_ENV": "production", "EXPO_PUBLIC_SUPABASE_MEDIAS_EVENT_OFFER_PATH": "event_offer_medias", "EXPO_PUBLIC_ALGOLIA_APP_ID": "DP7DN903MK", "__DEV__": "0"}, "ios": {"resourceClass": "medium"}, "android": {"buildType": "app-bundle"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "1552802200", "appleTeamId": "NB8WWTRV6R"}, "android": {"track": "production", "releaseStatus": "completed"}}}}