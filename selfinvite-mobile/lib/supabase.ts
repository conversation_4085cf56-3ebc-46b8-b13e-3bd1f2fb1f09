import { AppState, Platform } from 'react-native'
import 'react-native-url-polyfill/auto'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { createClient, processLock } from '@supabase/supabase-js'
import logger from '@/common/logger'

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://bdgfzohfcqauvxahlfjw.supabase.co';
const supabaseAuthUrl = Platform.select({
  ios: 'https://bdgfzohfcqauvxahlfjw.supabase.co',
  android: __DEV__ && process.env.EXPO_PUBLIC_APP_ENV === 'development'
    ? 'http://10.0.2.2:54321' //'http://127.0.0.1:54321'
    : 'https://bdgfzohfcqauvxahlfjw.supabase.co/auth',
  default: process.env.EXPO_PUBLIC_SUPABASE_URL,
}) || 'https://bdgfzohfcqauvxahlfjw.supabase.co';

logger.info(`SupabaseUrl URL: ${supabaseUrl}`);
logger.info(`SupabaseAuthUrl URL: ${supabaseAuthUrl}`);

const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    lock: processLock,
  },
})

const supabaseAuth = createClient(supabaseAuthUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    lock: processLock,
  },
})

const supabaseStorage = createClient(supabaseAuthUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    lock: processLock,
  },
})

// For backward compatibility and clarity, you can still export these aliases,
// but they now point to the same unified client.
export { supabaseAuth, supabaseStorage, supabase };
export default supabase;

// Tells Supabase Auth to continuously refresh the session automatically
// if the app is in the foreground. When this is added, you will continue
// to receive `onAuthStateChange` events with the `TOKEN_REFRESHED` or
// `SIGNED_OUT` event if the user's session is terminated. This should
// only be registered once.
AppState.addEventListener('change', (state) => {
  logger.info(`AppState: ${state}`)
  if (state === 'active') {
    supabase.auth.startAutoRefresh()
  } else {
    supabase.auth.stopAutoRefresh()
  }
})

// Set up token refresh listener to ensure new tokens are saved
supabase.auth.onAuthStateChange((event, session) => {
  logger.info(`Auth state changed: ${event} ${session?.user?.email}`)
  
  if (event === 'TOKEN_REFRESHED') {
    logger.info('Token refreshed, session updated')
    // The session is automatically persisted by Supabase
    // but we can add additional logging here
  }
  
  if (event === 'SIGNED_IN') {
    logger.info('User signed in, session saved')
  }
  
  if (event === 'SIGNED_OUT') {
    logger.info('User signed out, session cleared')
  }
})